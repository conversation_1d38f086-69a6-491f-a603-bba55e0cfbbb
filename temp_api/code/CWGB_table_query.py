

CWGB_table = {
    "LTHX": {
        "CWGB1": [
            (2000, 195),
            (3000, 207),
            (5000, 232),
        ],
        "CWGB2": [
            (2000, 125),
            (3000, 137),
            (5000, 162),
        ]
    },
    "LTHX Car": {
        "CWGB2": [
            (None, 475)
        ]
    },
    "LTHW": {
        "CWGB1": [
            (2000, 195),
            (3000, 207),
            (5000, 232),
        ],
        "CWGB2": [
            (2000, 125),
            (3000, 137),
            (5000, 162),
        ]
    },
    "LTHW Car": {
        "CWGB2": [
            (None, 475)
        ]
    },
    "EVIK": {
         "CWGB1": [
            (2000, 190),
        ],
        "CWGB2": [
            (None, 125),
        ]
    },
    "EVIN": {
         "CWGB1": [
            (2000, 190),
        ],
        "CWGB2": [
            (None, 125),
        ]
    },
    "LTK": {
         "CWGB1": [
            (2000, 190),
        ],
        "CWGB2": [
            (None, 125),
        ]
    }
}

def query_CWGB_value(Lift_Model, component, Capacity):
    if component not in CWGB_table[Lift_Model]:
        return None

    rules = CWGB_table[Lift_Model][component]

     # 如果是固定值（只有一个 None）
    if len(rules) == 1 and rules[0][0] is None:
        return rules[0][1]

    for cap, value in rules:
        if Capacity <= cap:
            return value
    return None