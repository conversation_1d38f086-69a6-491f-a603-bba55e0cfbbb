import os
from pathlib import Path




# ----------------------- 自定义环境参数 -----------------------
# 新增的 env 参数在这个区域引入

# 是否使用PostgreSQL数据库
USE_POSTGRES_DB = os.environ.get('USE_POSTGRES_DB', 0) == '1'
# 是否为生产环境
IS_PRODUCTION = os.environ.get('IS_PRODUCTION', '0') == '1'
# 主机地址
HOST_URL = os.environ.get('HOST_URL')
# ----------------------- 自定义环境参数 -----------------------


# 项目根目录
BASE_DIR = Path(__file__).resolve().parent.parent


# 项目密钥
SECRET_KEY = 'django-insecure-@af*cop_yug@jgemz=g+34yy)@w(!!^qu3fv=hnnk-jazl-22%'

# 是否开启调试模式
DEBUG = not IS_PRODUCTION

# 允许的主机
ALLOWED_HOSTS = [
    'localhost', '127.0.0.1', "*************","midea-demo.mountex.online","mdv-dashboard.vercel.app","midea.page.mountex.net","https://midea.linvol.mountex.net","https://midea.dashboard.mountex.online"]

# CSRF
CSRF_TRUSTED_ORIGINS = ['http://*************:7777',"https://midea-demo.mountex.online","https://mdv-dashboard.vercel.app","https://midea.linvol.mountex.net","https://midea.dashboard.mountex.online"]

# 跨域
CORS_ALLOWED_ORIGINS = [
    "http://localhost:3000",
    "http://localhost:3001",
    "http://localhost:5173",
    "http://localhost:63342",
    "http://127.0.0.1:8000",
    "http://*************:7777",
    "https://midea-demo.mountex.online",
    "https://mdv-dashboard.vercel.app",
    "https://midea.page.mountex.net",
    "https://midea.linvol.mountex.net",
    "https://midea.dashboard.mountex.online"
]



# 安装的应用
INSTALLED_APPS = [
    'simpleui',
    'corsheaders',
    'rest_framework',
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    
    'user',
    'temp_api',
    'customer',
    'language',
    'staff',
    'file',
    'opportunity',

]


# 中间件
MIDDLEWARE = [
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

# 根 URL 配置
ROOT_URLCONF = 'MideaBackend.urls'


# 模板
TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [BASE_DIR / 'templates'],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]


# WSGI 应用
WSGI_APPLICATION = 'MideaBackend.wsgi.application'


# 数据库
if USE_POSTGRES_DB != 0:
    DATABASES = {
        'default': {
            'ENGINE': 'django.db.backends.postgresql',
            'NAME': os.environ.get('POSTGRES_DB_NAME', ''),
            'USER': os.environ.get('POSTGRES_DB_USER', ''),
            'PASSWORD': os.environ.get('POSTGRES_DB_PASSWORD', ''),
            'HOST': os.environ.get('POSTGRES_DB_HOST', '127.0.0.1'),
            'PORT': os.environ.get('POSTGRES_DB_PORT', '5432'),
        }
    }
else:
    DATABASES = {
        'default': {
            'ENGINE': 'django.db.backends.sqlite3',
            'NAME': BASE_DIR / 'db.sqlite3',
        }
    }


# 密码验证
# https://docs.djangoproject.com/en/5.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


# 国际化和本地化
# https://docs.djangoproject.com/en/5.2/topics/i18n/

LANGUAGE_CODE = 'zh-hans'

TIME_ZONE = 'Asia/Shanghai'

USE_I18N = True

USE_TZ = True


# 静态文件 (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/5.2/howto/static-files/


STATIC_URL = 'static/'

STATICFILES_DIRS = (
    os.path.join(BASE_DIR, 'static'),
)

MEDIA_URL = '/media/'
MEDIA_ROOT = os.path.join(BASE_DIR, 'media')

if IS_PRODUCTION:
    STATIC_ROOT = '/var/www/media/static'
    MEDIA_ROOT = '/var/www/media/media'


# 默认主键字段类型
# https://docs.djangoproject.com/en/5.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'


SYSTEM_NAME = 'Midea Dashboard'
SIMPLEUI_HOME_TITLE = SYSTEM_NAME
SIMPLEUI_HOME_QUICK = True
SIMPLEUI_HOME_ACTION = True
SIMPLEUI_HOME_INFO = False
SIMPLEUI_LOGO = '/static/logo.png'
SIMPLEUI_ANALYSIS = False
SIMPLEPRO_MONIT_DISPLAY = True

# REST_FRAMEWORK = {
#        'EXCEPTION_HANDLER': 'core.authorization.midea_exception_handler'
#    }



# s3 存储
STORAGES = {
    "default": {
        "BACKEND": "storages.backends.s3.S3Storage",
        "OPTIONS": {
            "access_key": os.environ.get('AWS_ACCESS_KEY_ID', ''),
            "secret_key": os.environ.get('AWS_SECRET_ACCESS_KEY', ''),
            "bucket_name": os.environ.get('AWS_STORAGE_BUCKET_NAME', ''),
            "region_name": os.environ.get('AWS_S3_REGION_NAME', ''),
            # 明确指定端点URL以解决区域兼容性问题
            "endpoint_url": f"https://s3.{os.environ.get('AWS_S3_REGION_NAME', 'ap-east-1')}.amazonaws.com",
            "object_parameters": {
                "CacheControl": "max-age=86400",
            },
            "querystring_auth": True,  # 启用签名访问
            "querystring_expire": 3600,  # 签名链接过期时间（秒），默认1小时
            "file_overwrite": False,
            "default_acl": None,  # 不设置默认ACL，依赖bucket策略
            # 确保使用正确的签名版本
            "signature_version": "s3v4",
        },
    },
    "staticfiles": {
        "BACKEND": "django.contrib.staticfiles.storage.StaticFilesStorage",
    },
}
